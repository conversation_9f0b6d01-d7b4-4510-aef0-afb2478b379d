services:

  write_db:
    image: postgres:15
    environment:
      POSTGRES_USER: ${WRITE_DB_USER}
      POSTGRES_PASSWORD: ${WRITE_DB_PASS}
      POSTGRES_DB: ${WRITE_DB_NAME}
    volumes:
      - write_db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${WRITE_DB_USER} -d ${WRITE_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  read_db:
    image: postgres:15
    environment:
      POSTGRES_USER: ${READ_DB_USER}
      POSTGRES_PASSWORD: ${READ_DB_PASS}
      POSTGRES_DB: ${READ_DB_NAME}
    volumes:
      - read_db-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${READ_DB_USER} -d ${READ_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  app:
    container_name: oms-api
    build: .
    command: sh -c "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"
    stdin_open: true
    tty: true
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      write_db:
        condition: service_healthy
      read_db:
        condition: service_healthy
    volumes:
      - ./application:/application

volumes:
  write_db-data:
  read_db-data: