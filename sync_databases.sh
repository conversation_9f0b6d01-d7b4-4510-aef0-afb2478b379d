#!/bin/bash

# Database synchronization script for development
# This script copies schema and data from Write DB to Read DB

set -e

# Load environment variables
source .env

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Rozana OMS Database Sync Script ===${NC}"
echo -e "${YELLOW}This script will sync Write DB to Read DB${NC}"
echo ""

# Function to check if database exists and is accessible
check_db_connection() {
    local container=$1
    local user=$2
    local dbname=$3
    local db_label=$4
    
    echo -e "${BLUE}Checking ${db_label} connection...${NC}"
    
    if docker exec $container pg_isready -U $user -d $dbname > /dev/null 2>&1; then
        echo -e "${GREEN}✓ ${db_label} is accessible${NC}"
        return 0
    else
        echo -e "${RED}✗ ${db_label} is not accessible${NC}"
        return 1
    fi
}

# Function to run SQL command on a database
run_sql() {
    local container=$1
    local user=$2
    local dbname=$3
    local sql=$4
    local description=$5
    
    echo -e "${BLUE}${description}...${NC}"
    if docker exec $container psql -U $user -d $dbname -c "$sql" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ ${description} completed${NC}"
        return 0
    else
        echo -e "${RED}✗ ${description} failed${NC}"
        return 1
    fi
}

# Check if containers are running
echo -e "${BLUE}Checking Docker containers...${NC}"
if ! docker ps | grep -q "rozana-oms-service-write_db-1"; then
    echo -e "${RED}✗ Write DB container is not running${NC}"
    exit 1
fi

if ! docker ps | grep -q "rozana-oms-service-read_db-1"; then
    echo -e "${RED}✗ Read DB container is not running${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Both database containers are running${NC}"

# Check database connections
check_db_connection "rozana-oms-service-write_db-1" "$WRITE_DB_USER" "$WRITE_DB_NAME" "Write DB"
check_db_connection "rozana-oms-service-read_db-1" "$READ_DB_USER" "$READ_DB_NAME" "Read DB"

echo ""
echo -e "${YELLOW}Starting database synchronization...${NC}"
echo ""

# Step 1: Drop existing tables in Read DB (if any)
echo -e "${BLUE}Step 1: Cleaning Read DB...${NC}"
run_sql "rozana-oms-service-read_db-1" "$READ_DB_USER" "$READ_DB_NAME" "DROP SCHEMA IF EXISTS public CASCADE; CREATE SCHEMA public;" "Dropping existing schema in Read DB"

# Step 2: Dump schema from Write DB
echo -e "${BLUE}Step 2: Dumping schema from Write DB...${NC}"
docker exec rozana-oms-service-write_db-1 pg_dump -U $WRITE_DB_USER -d $WRITE_DB_NAME --schema-only --no-owner --no-privileges > /tmp/write_db_schema.sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Schema dumped successfully${NC}"
else
    echo -e "${RED}✗ Schema dump failed${NC}"
    exit 1
fi

# Step 3: Restore schema to Read DB
echo -e "${BLUE}Step 3: Restoring schema to Read DB...${NC}"
docker exec -i rozana-oms-service-read_db-1 psql -U $READ_DB_USER -d $READ_DB_NAME < /tmp/write_db_schema.sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Schema restored to Read DB${NC}"
else
    echo -e "${RED}✗ Schema restore failed${NC}"
    exit 1
fi

# Step 4: Dump data from Write DB
echo -e "${BLUE}Step 4: Dumping data from Write DB...${NC}"
docker exec rozana-oms-service-write_db-1 pg_dump -U $WRITE_DB_USER -d $WRITE_DB_NAME --data-only --no-owner --no-privileges > /tmp/write_db_data.sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Data dumped successfully${NC}"
else
    echo -e "${RED}✗ Data dump failed${NC}"
    exit 1
fi

# Step 5: Restore data to Read DB
echo -e "${BLUE}Step 5: Restoring data to Read DB...${NC}"
docker exec -i rozana-oms-service-read_db-1 psql -U $READ_DB_USER -d $READ_DB_NAME < /tmp/write_db_data.sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Data restored to Read DB${NC}"
else
    echo -e "${RED}✗ Data restore failed${NC}"
    exit 1
fi

# Step 6: Verify synchronization
echo -e "${BLUE}Step 6: Verifying synchronization...${NC}"

# Count tables in both databases
WRITE_DB_TABLES=$(docker exec rozana-oms-service-write_db-1 psql -U $WRITE_DB_USER -d $WRITE_DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')
READ_DB_TABLES=$(docker exec rozana-oms-service-read_db-1 psql -U $READ_DB_USER -d $READ_DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')

echo "Tables in Write DB: $WRITE_DB_TABLES"
echo "Tables in Read DB: $READ_DB_TABLES"

if [ "$WRITE_DB_TABLES" = "$READ_DB_TABLES" ]; then
    echo -e "${GREEN}✓ Table count matches${NC}"
else
    echo -e "${RED}✗ Table count mismatch${NC}"
fi

# Check if orders table exists in both databases
if docker exec rozana-oms-service-write_db-1 psql -U $WRITE_DB_USER -d $WRITE_DB_NAME -c "\dt orders" | grep -q "orders"; then
    echo -e "${GREEN}✓ Orders table exists in Write DB${NC}"
else
    echo -e "${YELLOW}⚠ Orders table not found in Write DB${NC}"
fi

if docker exec rozana-oms-service-read_db-1 psql -U $READ_DB_USER -d $READ_DB_NAME -c "\dt orders" | grep -q "orders"; then
    echo -e "${GREEN}✓ Orders table exists in Read DB${NC}"
else
    echo -e "${YELLOW}⚠ Orders table not found in Read DB${NC}"
fi

# Clean up temporary files
rm -f /tmp/write_db_schema.sql /tmp/write_db_data.sql

echo ""
echo -e "${GREEN}=== Database synchronization completed! ===${NC}"
echo -e "${BLUE}Both databases now have identical schema and data.${NC}"
echo ""
echo -e "${YELLOW}Note: This script should only be used in development environments.${NC}"
echo -e "${YELLOW}In production, use proper replication or data pipeline solutions.${NC}"
