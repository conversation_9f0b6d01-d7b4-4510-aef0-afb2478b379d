WRITE_DB_USER=user
WRITE_DB_PASS=password
WRITE_DB_NAME=oms_db

READ_DB_USER=read_user
READ_DB_PASS=read_pass
READ_DB_NAME=read_db

WRITE_DB_URL=****************************************/oms_db

READ_DB_URL=*********************************************/read_db

ALEMBIC_WRITE_DB_URL=postgresql+asyncpg://user:password@write_db:5432/oms_db

FIREBASE_CREDENTIALS_PATH=app/firebase-sample.json

LOG_LEVEL=INFO

ALLOWED_ORIGINS=

DEV_MODE=TRUE
DEV_AUTH_TOKEN=test-token
DEV_CUST_ID=CUSTUSER-001
