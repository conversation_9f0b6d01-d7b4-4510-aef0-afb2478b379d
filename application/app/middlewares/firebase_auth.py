from fastapi import Request
from fastapi.responses import JSONResponse
from firebase_admin import auth
from starlette.middleware.base import BaseHTTPMiddleware
import os
import logging

logger = logging.getLogger(__name__)

class FirebaseAuthMiddleware(BaseHTTPMiddleware):
    """Validate Firebase ID tokens and attach user info to `request.state`."""

    def __init__(self, app, excluded_paths: list[str] | None = None):
        super().__init__(app)
        self.excluded_paths = excluded_paths or ["/health", "/docs", "/openapi.json", "/redoc"]
        self.dev_mode = os.getenv("DEV_MODE", "FALSE") == "TRUE"
        self.dev_token = os.getenv("DEV_AUTH_TOKEN", "test-token")
        self.DEV_CUST_ID = os.getenv("DEV_CUST_ID", "CUSTUSER-001")
        logger.info(f"Firebase Auth Middleware initialized - Dev mode: {self.dev_mode}, Dev token: {self.dev_token}")

    async def dispatch(self, request: Request, call_next):
        # Skip auth for health or other excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)

        # Skip auth for OPTIONS requests (preflight requests)
        if request.method == "OPTIONS":
            return await call_next(request)

        auth_header = request.headers.get("authorization")
        if not auth_header:
            return JSONResponse(status_code=401, content={"detail": "Unauthorized"})

        logger.info(f"Auth header received: {auth_header}")

        # Development mode: accept test token
        if self.dev_mode and auth_header == f"Bearer {self.dev_token}":
            logger.info(f"Development mode authentication successful for user: {self.DEV_CUST_ID}")
            request.state.user_id = self.DEV_CUST_ID
            request.state.phone_number = "+**********"
            return await call_next(request)

        # Verify the Firebase ID token
        try:
            # Remove 'Bearer ' prefix if present
            token = auth_header.replace("Bearer ", "") if auth_header.startswith("Bearer ") else auth_header
            decoded_token = auth.verify_id_token(token)
            request.state.user_id = decoded_token.get("user_id")
            request.state.phone_number = decoded_token.get("phone_number")
        except Exception as e:
            logger.error(f"Firebase token verification failed: {e}")
            return JSONResponse(status_code=401, content={"detail": "Invalid token"})

        return await call_next(request)
