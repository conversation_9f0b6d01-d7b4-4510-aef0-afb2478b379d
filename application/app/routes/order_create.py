from fastapi import APIRouter, HTTPException, Query, Request
import logging
from app.database import get_db, get_read_db
from app.services.order_command_service import OrderCommandService
from app.services.order_query_service import OrderQueryService
from app.models.orders import OrderCreate, OrderResponse, BulkOrderCreate, BulkOrderResponse

#validator
from app.validations.orders import OrderCreateValidator

router = APIRouter()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API Endpoints

@router.post("/create_order", response_model=OrderResponse)
async def create_order(order: OrderCreate, request: Request):
    """Create new order and order items"""
    async with get_db() as db_conn:
        try:
            user_id = getattr(request.state, 'user_id', None)
            print("user_id", user_id)
            validator = OrderCreateValidator(order, user_id)
            validator.validate()

            logger.info(f"Create order requested by user_id={user_id}")
            service = OrderCommandService()
            result = await service.create_order(order.model_dump(), db_conn)

            if not result.get("success", False):
                raise HTTPException(status_code=400, detail=result.get("message", "Failed to create order"))

            return result
        except HTTPException:
            raise
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/get_order_details")
async def get_order_details(order_id: str = Query(..., description="Order ID to retrieve")):
    """Returns complete order data including order items - uses read database (READ_DB)"""
    async with get_read_db() as db_conn:
        try:
            service = OrderQueryService(db_conn)
            order = service.get_order_by_id(order_id)

            if not order:
                raise HTTPException(status_code=404, detail="Order not found")

            return order
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting order details {order_id}: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/get_all_orders") 
async def get_all_orders(request: Request, limit: int = 20, offset: int = 0, sort: str = "desc"):
    """Returns all orders for a specific user - uses read database (READ_DB)"""
    async with get_read_db() as db_conn:
        user_id = getattr(request.state, 'user_id', None)
        try:
            service = OrderQueryService(db_conn)

            validator = OrderCreateValidator(user_id=user_id)
            validator.validate_page_size(limit, offset)

            orders = service.get_all_orders(user_id, limit, offset, sort)

            if not orders:
                raise HTTPException(status_code=404, detail="No orders found")

            return orders
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting all orders for user {user_id}: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/create_bulk_orders", response_model=BulkOrderResponse)
async def create_bulk_orders(payload: BulkOrderCreate, request: Request):
    user_id = getattr(request.state, 'user_id', None)
    if not user_id and payload.orders:
        user_id = payload.orders[0].customer_id

    try:
        async with get_db() as db:
            service = OrderCommandService()
            results = await service.create_bulk_orders(payload.orders, user_id, db)
            return BulkOrderResponse(
                success=True, 
                message=f"Successfully created {len(results)} orders", 
                results=results
            )

    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.error(f"Bulk order error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
