from fastapi import APIRouter, HTTPException, Request
from app.database import get_db_pool, get_read_db_pool, get_db
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/health")
async def health_check(request: Request):
    user_id = request.scope.get('user_id', 'N/A')
    phone_number = request.scope.get('phone_number', 'N/A')
    print(user_id, phone_number)
    return {"status": "healthy", "version": "4.0.0", "service": "rozana-oms", "user_id": user_id, "phone_number": phone_number}