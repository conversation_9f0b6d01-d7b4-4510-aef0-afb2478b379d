from fastapi import APIRouter, HTTPException, Query, Request
import logging
from typing import List
from app.services.inventory_service import InventoryService
from app.models.inventory import (
    StockItem, StockUpdate, BulkStockUpdate, StockCheck, BulkStockCheck,
    StockResponse, StockOperationResponse, BulkStockOperationResponse
)

router = APIRouter()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize inventory service
inventory_service = InventoryService()

@router.post("/set_stock", response_model=StockOperationResponse)
async def set_stock(stock_item: StockItem, request: Request):
    """
    Set stock quantity for a specific SKU
    
    Why needed:
    - Initialize stock levels for new products
    - Update stock after inventory counts
    - Manual stock adjustments
    
    Request Body:
    {
        "sku": "ITEM-001",
        "quantity": 100
    }
    
    Response:
    {
        "success": true,
        "message": "Stock set successfully for ITEM-001",
        "sku": "ITEM-001",
        "quantity": 100
    }
    """
    try:
        user_id = getattr(request.state, 'user_id', None)
        logger.info(f"Set stock requested by user_id={user_id} for SKU={stock_item.sku}")
        
        result = inventory_service.set_stock(stock_item.sku, stock_item.quantity)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting stock for {stock_item.sku}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/bulk_set_stock", response_model=BulkStockOperationResponse)
async def bulk_set_stock(bulk_stock: BulkStockUpdate, request: Request):
    """
    Set stock for multiple SKUs in bulk
    
    Why needed:
    - Efficient bulk inventory updates
    - Initial inventory setup
    - Batch stock adjustments
    
    Request Body:
    {
        "items": [
            {"sku": "ITEM-001", "quantity": 100},
            {"sku": "ITEM-002", "quantity": 50}
        ]
    }
    
    Response:
    {
        "success": true,
        "message": "Bulk stock update completed: 2 successful, 0 failed",
        "results": [...],
        "successful_count": 2,
        "failed_count": 0
    }
    """
    try:
        user_id = getattr(request.state, 'user_id', None)
        logger.info(f"Bulk set stock requested by user_id={user_id} for {len(bulk_stock.items)} items")
        
        result = inventory_service.bulk_set_stock(bulk_stock.items)
        
        if not result.success and result.failed_count == len(bulk_stock.items):
            raise HTTPException(status_code=400, detail=result.message)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk stock update: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/get_stock", response_model=StockResponse)
async def get_stock(sku: str = Query(..., description="Product SKU to check")):
    """
    Get comprehensive stock information for a SKU
    
    Why needed:
    - Check current stock levels
    - View reserved vs available stock
    - Inventory monitoring and reporting
    
    Query Parameters:
    - sku: Product SKU (required)
    
    Response:
    {
        "sku": "ITEM-001",
        "total_stock": 100,
        "reserved_stock": 20,
        "available_stock": 80
    }
    """
    try:
        logger.info(f"Stock info requested for SKU={sku}")
        
        result = inventory_service.get_stock_info(sku)
        return result
        
    except Exception as e:
        logger.error(f"Error getting stock info for {sku}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/check_availability")
async def check_stock_availability(stock_check: StockCheck):
    """
    Check if sufficient stock is available for a specific quantity
    
    Why needed:
    - Pre-order validation
    - Real-time availability checking
    - Inventory planning
    
    Request Body:
    {
        "sku": "ITEM-001",
        "required_quantity": 10
    }
    
    Response:
    {
        "sku": "ITEM-001",
        "required_quantity": 10,
        "available": true,
        "message": "Stock available: 80 units",
        "available_stock": 80
    }
    """
    try:
        logger.info(f"Stock availability check for SKU={stock_check.sku}, quantity={stock_check.required_quantity}")
        
        available, message = inventory_service.check_stock_availability(
            stock_check.sku, 
            stock_check.required_quantity
        )
        
        stock_info = inventory_service.get_stock_info(stock_check.sku)
        
        return {
            "sku": stock_check.sku,
            "required_quantity": stock_check.required_quantity,
            "available": available,
            "message": message,
            "available_stock": stock_info.available_stock
        }
        
    except Exception as e:
        logger.error(f"Error checking stock availability for {stock_check.sku}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/bulk_check_availability")
async def bulk_check_stock_availability(bulk_check: BulkStockCheck):
    """
    Check stock availability for multiple items
    
    Why needed:
    - Order validation before processing
    - Batch availability checking
    - Efficient multi-item validation
    
    Request Body:
    {
        "items": [
            {"sku": "ITEM-001", "required_quantity": 10},
            {"sku": "ITEM-002", "required_quantity": 5}
        ]
    }
    
    Response:
    {
        "all_available": true,
        "results": [
            {
                "sku": "ITEM-001",
                "required_quantity": 10,
                "available": true,
                "message": "Stock available: 80 units"
            },
            {
                "sku": "ITEM-002", 
                "required_quantity": 5,
                "available": true,
                "message": "Stock available: 45 units"
            }
        ]
    }
    """
    try:
        items_data = [{"sku": item.sku, "quantity": item.required_quantity} for item in bulk_check.items]
        logger.info(f"Bulk stock availability check for {len(items_data)} items")
        
        all_available, results = inventory_service.bulk_check_availability(items_data)
        
        return {
            "all_available": all_available,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error in bulk stock availability check: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/reserve_stock")
async def reserve_stock(stock_check: StockCheck, request: Request):
    """
    Reserve stock for a specific SKU (for testing purposes)
    
    Why needed:
    - Manual stock reservation testing
    - Debugging inventory issues
    - Administrative stock management
    
    Note: In production, stock is automatically reserved during order creation
    """
    try:
        user_id = getattr(request.state, 'user_id', None)
        logger.info(f"Manual stock reservation by user_id={user_id} for SKU={stock_check.sku}")
        
        success = inventory_service.inventory_manager.reserve_stock(
            stock_check.sku, 
            stock_check.required_quantity
        )
        
        if success:
            return {
                "success": True,
                "message": f"Reserved {stock_check.required_quantity} units of {stock_check.sku}",
                "sku": stock_check.sku,
                "quantity": stock_check.required_quantity
            }
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"Failed to reserve {stock_check.required_quantity} units of {stock_check.sku}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reserving stock for {stock_check.sku}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/release_stock")
async def release_stock(stock_check: StockCheck, request: Request):
    """
    Release reserved stock for a specific SKU (for testing purposes)
    
    Why needed:
    - Manual stock release testing
    - Debugging inventory issues
    - Administrative stock management
    
    Note: In production, stock is automatically released during order cancellation
    """
    try:
        user_id = getattr(request.state, 'user_id', None)
        logger.info(f"Manual stock release by user_id={user_id} for SKU={stock_check.sku}")
        
        success = inventory_service.inventory_manager.release_stock(
            stock_check.sku, 
            stock_check.required_quantity
        )
        
        if success:
            return {
                "success": True,
                "message": f"Released {stock_check.required_quantity} units of {stock_check.sku}",
                "sku": stock_check.sku,
                "quantity": stock_check.required_quantity
            }
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"Failed to release {stock_check.required_quantity} units of {stock_check.sku}"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error releasing stock for {stock_check.sku}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/inventory_status")
async def get_inventory_status(request: Request):
    """
    Get overall inventory status and statistics
    
    Why needed:
    - Inventory monitoring dashboard
    - System health checking
    - Administrative overview
    
    Response:
    {
        "total_skus": 150,
        "total_stock": 5000,
        "total_reserved": 500,
        "total_available": 4500,
        "redis_connection": "healthy"
    }
    """
    try:
        user_id = getattr(request.state, 'user_id', None)
        logger.info(f"Inventory status requested by user_id={user_id}")
        
        # This is a simplified version - in production you might want more detailed stats
        from app.redis_client import get_redis_client
        
        redis_client = get_redis_client()
        redis_healthy = redis_client.ping()
        
        return {
            "redis_connection": "healthy" if redis_healthy else "unhealthy",
            "message": "Inventory system operational",
            "timestamp": "2025-07-09T12:00:00"
        }
        
    except Exception as e:
        logger.error(f"Error getting inventory status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
