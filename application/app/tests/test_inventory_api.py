#!/usr/bin/env python3
"""
Comprehensive test script for OMS with inventory management
Tests the complete flow from stock setup to order creation and status updates
"""

import requests
import json
import time
from datetime import datetime

# API base URL
BASE_URL = "http://localhost:8000"
AUTH_TOKEN = "test-token"
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {AUTH_TOKEN}"
}

def print_section(title):
    """Print a formatted section header"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_result(description, response):
    """Print formatted test result"""
    status_emoji = "✅" if response.status_code < 400 else "❌"
    print(f"{status_emoji} {description}")
    print(f"   Status: {response.status_code}")
    try:
        response_data = response.json()
        print(f"   Response: {json.dumps(response_data, indent=2, default=str)}")
        return response_data
    except:
        print(f"   Response: {response.text}")
        return None

def test_health_check():
    """Test the health check endpoint"""
    print_section("Health Check")
    response = requests.get(f"{BASE_URL}/health")
    return print_result("Health check", response)

def setup_initial_stock():
    """Set up initial stock for testing"""
    print_section("Setting Up Initial Stock")
    
    # Set individual stock items
    stock_items = [
        {"sku": "ITEM-001", "quantity": 100},
        {"sku": "ITEM-002", "quantity": 50},
        {"sku": "ITEM-003", "quantity": 75},
        {"sku": "ITEM-004", "quantity": 25}
    ]
    
    results = []
    for item in stock_items:
        response = requests.post(
            f"{BASE_URL}/inventory/set_stock",
            headers=HEADERS,
            json=item
        )
        result = print_result(f"Set stock for {item['sku']}", response)
        results.append(result)
    
    # Test bulk stock setting
    bulk_items = [
        {"sku": "ITEM-005", "quantity": 200},
        {"sku": "ITEM-006", "quantity": 150}
    ]
    
    response = requests.post(
        f"{BASE_URL}/inventory/bulk_set_stock",
        headers=HEADERS,
        json={"items": bulk_items}
    )
    print_result("Bulk set stock", response)
    
    return results

def test_stock_queries():
    """Test various stock query endpoints"""
    print_section("Testing Stock Queries")
    
    # Get individual stock info
    response = requests.get(
        f"{BASE_URL}/inventory/get_stock?sku=ITEM-001",
        headers=HEADERS
    )
    print_result("Get stock info for ITEM-001", response)
    
    # Check availability
    response = requests.post(
        f"{BASE_URL}/inventory/check_availability",
        headers=HEADERS,
        json={"sku": "ITEM-001", "required_quantity": 10}
    )
    print_result("Check availability for 10 units of ITEM-001", response)
    
    # Bulk availability check
    response = requests.post(
        f"{BASE_URL}/inventory/bulk_check_availability",
        headers=HEADERS,
        json={
            "items": [
                {"sku": "ITEM-001", "required_quantity": 5},
                {"sku": "ITEM-002", "required_quantity": 3}
            ]
        }
    )
    print_result("Bulk availability check", response)

def test_successful_order_creation():
    """Test creating an order with sufficient stock"""
    print_section("Testing Successful Order Creation")
    
    order_data = {
        "customer_id": "CUSTUSER-001",
        "customer_name": "John Doe",
        "facility_id": "FAC-001",
        "facility_name": "Main Warehouse",
        "status": "pending",
        "total_amount": 99.99,
        "items": [
            {
                "sku": "ITEM-001",
                "quantity": 2,
                "unit_price": 45.00,
                "sale_price": 49.99
            },
            {
                "sku": "ITEM-002",
                "quantity": 1,
                "unit_price": 25.00,
                "sale_price": 29.99
            }
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/create_order",
        headers=HEADERS,
        json=order_data
    )
    
    result = print_result("Create order with sufficient stock", response)
    
    if result and result.get("success"):
        order_id = result.get("order_id")
        print(f"   📝 Order ID: {order_id}")
        
        # Verify stock reservation
        print("\n   Verifying stock reservation:")
        for item in order_data["items"]:
            response = requests.get(
                f"{BASE_URL}/inventory/get_stock?sku={item['sku']}",
                headers=HEADERS
            )
            stock_info = print_result(f"Stock info for {item['sku']} after order", response)
        
        return order_id
    
    return None

def test_insufficient_stock_order():
    """Test creating an order with insufficient stock"""
    print_section("Testing Insufficient Stock Scenario")
    
    order_data = {
        "customer_id": "CUSTUSER-001",
        "customer_name": "Jane Doe",
        "facility_id": "FAC-001",
        "facility_name": "Main Warehouse",
        "status": "pending",
        "total_amount": 999.99,
        "items": [
            {
                "sku": "ITEM-001",
                "quantity": 200,  # More than available
                "unit_price": 45.00,
                "sale_price": 49.99
            }
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/create_order",
        headers=HEADERS,
        json=order_data
    )
    
    print_result("Create order with insufficient stock (should fail)", response)

def test_order_status_updates(order_id):
    """Test order status updates and their inventory effects"""
    if not order_id:
        print("⚠️  Skipping order status tests - no valid order ID")
        return
    
    print_section("Testing Order Status Updates")
    
    # Confirm the order (should confirm stock usage)
    response = requests.put(
        f"{BASE_URL}/update_order_status",
        headers=HEADERS,
        json={"order_id": order_id, "status": "confirmed"}
    )
    print_result(f"Confirm order {order_id}", response)
    
    # Check stock after confirmation
    print("\n   Verifying stock after confirmation:")
    response = requests.get(
        f"{BASE_URL}/inventory/get_stock?sku=ITEM-001",
        headers=HEADERS
    )
    print_result("Stock info for ITEM-001 after confirmation", response)
    
    # Deliver the order
    response = requests.put(
        f"{BASE_URL}/update_order_status",
        headers=HEADERS,
        json={"order_id": order_id, "status": "delivered"}
    )
    print_result(f"Deliver order {order_id}", response)

def test_order_cancellation():
    """Test order cancellation and stock release"""
    print_section("Testing Order Cancellation")
    
    # Create a new order to cancel
    order_data = {
        "customer_id": "CUSTUSER-001",
        "customer_name": "Test User",
        "facility_id": "FAC-001",
        "facility_name": "Main Warehouse",
        "status": "pending",
        "total_amount": 49.99,
        "items": [
            {
                "sku": "ITEM-003",
                "quantity": 5,
                "unit_price": 10.00,
                "sale_price": 9.99
            }
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/create_order",
        headers=HEADERS,
        json=order_data
    )
    
    result = print_result("Create order for cancellation test", response)
    
    if result and result.get("success"):
        order_id = result.get("order_id")
        
        # Check stock before cancellation
        response = requests.get(
            f"{BASE_URL}/inventory/get_stock?sku=ITEM-003",
            headers=HEADERS
        )
        stock_before = print_result("Stock before cancellation", response)
        
        # Cancel the order
        response = requests.put(
            f"{BASE_URL}/update_order_status",
            headers=HEADERS,
            json={"order_id": order_id, "status": "cancelled"}
        )
        print_result(f"Cancel order {order_id}", response)
        
        # Check stock after cancellation
        response = requests.get(
            f"{BASE_URL}/inventory/get_stock?sku=ITEM-003",
            headers=HEADERS
        )
        stock_after = print_result("Stock after cancellation", response)

def test_inventory_status():
    """Test inventory status endpoint"""
    print_section("Testing Inventory Status")
    
    response = requests.get(
        f"{BASE_URL}/inventory/inventory_status",
        headers=HEADERS
    )
    print_result("Get inventory status", response)

def main():
    """Run all tests"""
    print("🚀 Starting Comprehensive OMS + Inventory Tests")
    print(f"📅 Test started at: {datetime.now()}")
    
    try:
        # Basic health check
        test_health_check()
        
        # Set up inventory
        setup_initial_stock()
        
        # Test stock queries
        test_stock_queries()
        
        # Test successful order creation
        order_id = test_successful_order_creation()
        
        # Test insufficient stock scenario
        test_insufficient_stock_order()
        
        # Test order status updates
        test_order_status_updates(order_id)
        
        # Test order cancellation
        test_order_cancellation()
        
        # Test inventory status
        test_inventory_status()
        
        print_section("Test Summary")
        print("🎉 All tests completed!")
        print("📊 Check the results above for any failures")
        print(f"📅 Test completed at: {datetime.now()}")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
