from typing import Dict, Optional
import logging
from psycopg.rows import dict_row

logger = logging.getLogger(__name__)

# class OrderQueryService:
#     """Service for handling order queries (Read operations) from single database"""

#     def __init__(self, db_conn):
#         self.db_conn = db_conn

#     def get_order_by_id(self, order_id: str) -> Optional[Dict]:
#         """Get single order by order_id from database with complete details"""
#         if not self.db_conn:
#             raise ValueError("Database is required for queries")

#         with self.db_conn.cursor() as cursor:
#             query = """
#                 SELECT 
#                     o.order_id,
#                     o.customer_id,
#                     o.customer_name,
#                     o.facility_id,
#                     o.facility_name,
#                     o.status,
#                     o.total_amount,
#                     o.eta,
#                     o.created_at,
#                     o.updated_at,
#                     COALESCE(jsonb_agg(
#                         jsonb_build_object(
#                             'sku', oi.sku,
#                             'quantity', oi.quantity,
#                             'unit_price', oi.unit_price,
#                             'sale_price', oi.sale_price,
#                             'status', oi.status
#                         )
#                     ), '[]'::jsonb) AS items
#                 FROM orders o
#                 LEFT JOIN order_items oi USING (order_id)
#                 WHERE o.order_id = %s
#                 GROUP BY o.order_id
#             """
#             cursor.execute(query, (order_id,))
#             row = cursor.fetchone()
#             if not row:
#                 return None
#             return {
#                 "id": row[0],
#                 "order_id": row[1],
#                 "customer_id": row[2],
#                 "customer_name": row[3],
#                 "facility_id": row[4],
#                 "facility_name": row[5],
#                 "status": row[6],
#                 "total_amount": float(row[7]),
#                 "eta": row[8],
#                 "created_at": row[9],
#                 "updated_at": row[10],
#                 "items": [item for item in row[11] if item]
#             }

class OrderQueryService:
    """Service for handling order queries (Read operations) from single database"""

    def __init__(self, db_conn):
        self.db_conn = db_conn

    def get_order_by_id(self, order_id: str) -> Optional[Dict]:
        """Get single order by order_id from database with complete details"""
        if not self.db_conn:
            raise ValueError("Database is required for queries")


        # Use dict_row row factory so we can access columns by name
        with self.db_conn.cursor(row_factory=dict_row) as cursor:
            sql = """
                SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                       o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta,
                       o.created_at, o.updated_at,
                       oi.sku, oi.quantity, oi.unit_price, oi.sale_price, oi.status AS item_status
                FROM orders o
                LEFT JOIN order_items oi ON oi.order_id = o.order_id
                WHERE o.order_id = %s
            """
            cursor.execute(sql, (order_id,))
            rows = cursor.fetchall()
            if not rows:
                return None

            header = rows[0]
            items = []

            for r in rows:
                if r["sku"] is not None:
                    item_dict = {
                        "sku": r["sku"],
                        "quantity": r["quantity"],
                        "unit_price": float(r["unit_price"]) if r["unit_price"] is not None else None,
                        "sale_price": float(r["sale_price"]) if r["sale_price"] is not None else None,
                        "status": r["item_status"],
                    }
                    items.append(item_dict)

            return {
                "order_id": header["order_id"],
                "customer_id": header["customer_id"],
                "customer_name": header["customer_name"],
                "facility_id": header["facility_id"],
                "facility_name": header["facility_name"],
                "status": header["status"],
                "total_amount": float(header["total_amount"]),
                "eta": header["eta"],
                "created_at": header["created_at"],
                "updated_at": header["updated_at"],
                "items": items,
            }
    
    def get_all_orders(self, customer_id: str, limit: int = 20, offset: int = 0, sort: str = "desc") -> list[Dict]:
        if not self.db_conn:
            raise ValueError("Database is required for queries")

        # Validate sort parameter
        if sort.lower() not in ["asc", "desc"]:
            sort = "desc"
        
        sort_order = "DESC" if sort.lower() == "desc" else "ASC"

        # Use dict_row row factory so we can access columns by name
        with self.db_conn.cursor(row_factory=dict_row) as cursor:
            # First, get the distinct orders with pagination
            orders_sql = f"""
                SELECT o.id, o.order_id, o.customer_id, o.customer_name,
                       o.facility_id, o.facility_name,
                       o.status, o.total_amount, o.eta,
                       o.created_at, o.updated_at
                FROM orders o
                WHERE o.customer_id = %s
                ORDER BY o.created_at {sort_order}
                LIMIT %s OFFSET %s
            """
            cursor.execute(orders_sql, (customer_id, limit, offset))
            orders = cursor.fetchall()
            
            if not orders:
                return []

            # Get all order IDs for fetching items
            order_ids = [order["order_id"] for order in orders]
            
            # Build placeholders for IN clause
            placeholders = ','.join(['%s'] * len(order_ids))
            
            # Fetch all items for these orders
            items_sql = f"""
                SELECT oi.order_id, oi.sku, oi.quantity, oi.unit_price, oi.sale_price, oi.status
                FROM order_items oi
                WHERE oi.order_id IN ({placeholders})
                ORDER BY oi.id
            """
            cursor.execute(items_sql, order_ids)
            items_rows = cursor.fetchall()
            
            # Group items by order_id
            items_by_order = {}
            for item in items_rows:
                order_id = item["order_id"]
                if order_id not in items_by_order:
                    items_by_order[order_id] = []
                items_by_order[order_id].append({
                    "sku": item["sku"],
                    "quantity": item["quantity"],
                    "unit_price": float(item["unit_price"]) if item["unit_price"] is not None else None,
                    "sale_price": float(item["sale_price"]) if item["sale_price"] is not None else None,
                    "status": item["status"],
                })
            
            # Combine orders with their items
            result = []
            for order in orders:
                order_data = {
                    "order_id": order["order_id"],
                    "customer_id": order["customer_id"],
                    "customer_name": order["customer_name"],
                    "facility_id": order["facility_id"],
                    "facility_name": order["facility_name"],
                    "status": order["status"],
                    "total_amount": float(order["total_amount"]) if order["total_amount"] is not None else None,
                    "eta": order["eta"],
                    "created_at": order["created_at"],
                    "updated_at": order["updated_at"],
                    "items": items_by_order.get(order["order_id"], []),
                }
                result.append(order_data)
            
            return result
