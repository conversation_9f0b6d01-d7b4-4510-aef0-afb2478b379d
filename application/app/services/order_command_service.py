from typing import Dict
import logging
import random
import string
from datetime import datetime, timedelta
from app.services.inventory_service import InventoryService

logger = logging.getLogger(__name__)

def generate_random_prefix() -> str:
    """Generate a random 4-character alphanumeric string for order ID prefix.

    Returns:
        str: A 4-character string containing uppercase letters and digits (e.g., 'A2K4', '489K', 'X7B9')
    """
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=4))

# class OrderCommandService:
    """Service for handling order commands (Create, Update, Cancel) with single database"""

    def __init__(self, db_conn=None):
        self.db_conn = db_conn

    async def create_order(self, order_data: Dict, db_conn=None) -> Dict:
        """Create order - Direct write to database with auto-generated order_id"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for create operation")

        try:
            logger.info(f"Creating order for customer {order_data['customer_id']}")

            # Validate required fields
            required_fields = ['customer_id', 'customer_name', 'facility_id', 'facility_name', 'status', 'total_amount']
            for field in required_fields:
                if field not in order_data or not order_data[field]:
                    raise ValueError(f"Missing required field: {field}")

            # Direct database write with transaction
            with conn.cursor() as cursor:
                try:
                    # Calculate ETA (24 hours from now as default)
                    from datetime import datetime, timedelta
                    eta = datetime.now() + timedelta(hours=24)

                    # Generate random prefix for order ID
                    random_prefix = generate_random_prefix()

                    # Insert order - order_id will be auto-generated by PostgreSQL using the random_prefix
                    order_query = """
                        INSERT INTO orders (
                            random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        RETURNING order_id, id
                    """

                    order_values = (
                        random_prefix,
                        order_data['customer_id'],
                        order_data['customer_name'],
                        order_data['facility_id'],
                        order_data['facility_name'],
                        order_data['status'],
                        order_data['total_amount'],
                        eta
                    )

                    cursor.execute(order_query, order_values)
                    result_row = cursor.fetchone()
                    generated_order_id = result_row[0]

                    # Insert order items
                    if order_data.get('items'):
                        item_query = """
                            INSERT INTO order_items (
                                order_id, sku, quantity, unit_price, sale_price, status
                            ) VALUES (%s, %s, %s, %s, %s, %s)
                        """

                        item_values = [
                            (
                                generated_order_id,
                                item['sku'],
                                item['quantity'],
                                item['unit_price'],
                                item['sale_price'],
                                order_data['status']
                            )
                            for item in order_data['items']
                        ]
                        cursor.executemany(item_query, item_values)

                    conn.commit()
                    logger.info(f"Order {generated_order_id} created successfully in database")

                    return {
                        "success": True,
                        "message": "Order created successfully",
                        "order_id": generated_order_id,
                        "eta": eta.isoformat()
                    }

                except Exception as db_error:
                    conn.rollback()
                    logger.error(f"Database error creating order: {db_error}")
                    raise

        except Exception as e:
            logger.error(f"Failed to create order: {e}")
            raise

    async def update_order_status(self, order_id: str, status: str, db_conn=None) -> Dict:
        """Update order status in both orders and order_items tables"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists
                cursor.execute("SELECT id FROM orders WHERE order_id = %s", (order_id,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                # Update order status
                cursor.execute("""
                    UPDATE orders
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                # Update order items status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                conn.commit()
                logger.info(f"Order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Order status updated to {status}",
                    "order_id": order_id
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update order status {order_id}: {e}")
            raise

    async def update_item_status(self, order_id: str, sku: str, status: str, db_conn=None) -> Dict:
        """Update status of a specific item within an order"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists
                cursor.execute("SELECT id FROM orders WHERE order_id = %s", (order_id,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                # Check if item exists in the order
                cursor.execute("""
                    SELECT id FROM order_items
                    WHERE order_id = %s AND sku = %s
                """, (order_id, sku))

                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                    }

                # Update specific item status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s AND sku = %s
                """, (status, order_id, sku))

                conn.commit()
                logger.info(f"Item {sku} in order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Item '{sku}' status updated to '{status}'",
                    "order_id": order_id,
                    "sku": sku,
                    "status": status
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update item status {order_id}/{sku}: {e}")
            raise

class OrderCommandService:
    """Service for handling order commands (Create, Update, Cancel) with single database and inventory verification"""

    def __init__(self, db_conn=None):
        self.db_conn = db_conn
        self.inventory_service = InventoryService()

    async def create_order(self, order_data: Dict, db_conn=None) -> Dict:
        """
        Create order with inventory verification - Enhanced with stock checking

        Flow:
        1. Validate required fields
        2. Check stock availability for all items
        3. Reserve stock for the order
        4. Create order in database
        5. If database fails, release reserved stock

        Why needed:
        - Prevents overselling by checking stock before order creation
        - Atomic operation ensures data consistency
        - Proper rollback mechanism on failures
        """
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for create operation")

        try:
            logger.info(f"Creating order for customer {order_data['customer_id']}")

            # Step 1: Validate required fields
            required_fields = ['customer_id', 'customer_name', 'facility_id', 'facility_name', 'status', 'total_amount']
            for field in required_fields:
                if field not in order_data or not order_data[field]:
                    raise ValueError(f"Missing required field: {field}")

            # Step 2: Check stock availability for all items
            if not order_data.get('items'):
                raise ValueError("Order must contain at least one item")

            logger.info(f"Checking stock availability for {len(order_data['items'])} items")
            all_available, availability_results = self.inventory_service.bulk_check_availability(order_data['items'])

            if not all_available:
                # Build detailed error message
                unavailable_items = [result for result in availability_results if not result.get('available', False)]
                error_messages = [f"{item['sku']}: {item['message']}" for item in unavailable_items]

                logger.warning(f"Stock unavailable for order: {error_messages}")
                return {
                    "success": False,
                    "message": "Insufficient stock for order",
                    "details": availability_results,
                    "unavailable_items": error_messages
                }

            # Step 3: Reserve stock for the order (before creating order)
            logger.info("Stock available, attempting to reserve stock")
            reservation_success, reservation_results = self.inventory_service.reserve_stock_for_order(
                order_data['items'],
                order_id="pending"  # Will be updated with actual order_id later
            )

            if not reservation_success:
                logger.error(f"Failed to reserve stock: {reservation_results}")
                return {
                    "success": False,
                    "message": "Failed to reserve stock for order",
                    "details": reservation_results
                }

            # Step 4: Create order in database (stock is now reserved)
            try:
                with conn.cursor() as cursor:
                    # Calculate ETA (24 hours from now as default)
                    eta = datetime.now() + timedelta(hours=24)

                    # Generate random prefix for order ID
                    random_prefix = generate_random_prefix()

                    # Insert order - order_id will be auto-generated by PostgreSQL
                    order_query = """
                        INSERT INTO orders (
                            random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        RETURNING order_id, id
                    """

                    order_values = (
                        random_prefix,
                        order_data['customer_id'],
                        order_data['customer_name'],
                        order_data['facility_id'],
                        order_data['facility_name'],
                        order_data['status'],
                        order_data['total_amount'],
                        eta
                    )

                    cursor.execute(order_query, order_values)
                    result_row = cursor.fetchone()
                    generated_order_id = result_row[0]

                    # Insert order items
                    item_query = """
                        INSERT INTO order_items (
                            order_id, sku, quantity, unit_price, sale_price, status
                        ) VALUES (%s, %s, %s, %s, %s, %s)
                    """

                    for item in order_data['items']:
                        item_values = (
                            generated_order_id,
                            item['sku'],
                            item['quantity'],
                            item['unit_price'],
                            item['sale_price'],
                            order_data['status']  # Set item status same as order status
                        )
                        cursor.execute(item_query, item_values)

                    conn.commit()
                    logger.info(f"Order {generated_order_id} created successfully with stock reserved")

                    return {
                        "success": True,
                        "message": "Order created successfully with stock reserved",
                        "order_id": generated_order_id,
                        "eta": eta.isoformat(),
                        "stock_reserved": True,
                        "reservation_details": reservation_results
                    }

            except Exception as db_error:
                # Step 5: Database failed, release reserved stock
                conn.rollback()
                logger.error(f"Database error creating order, releasing reserved stock: {db_error}")

                # Release the reserved stock since order creation failed
                release_success = self.inventory_service.release_stock_for_order(
                    order_data['items'],
                    order_id="failed_order"
                )

                if not release_success:
                    logger.critical(f"CRITICAL: Failed to release reserved stock after database error!")

                raise

        except Exception as e:
            logger.error(f"Failed to create order: {e}")
            raise

    async def update_order_status(self, order_id: str, status: str, db_conn=None) -> Dict:
        """
        Update order status with inventory management

        Flow:
        1. Check if order exists and get items
        2. Handle inventory based on status change
        3. Update database status

        Status handling:
        - 'confirmed': Confirm stock usage (reduce total stock)
        - 'cancelled': Release reserved stock
        - 'delivered': No inventory action (stock already confirmed)

        Why needed:
        - Manages inventory lifecycle with order status changes
        - Ensures stock levels remain accurate
        - Handles different status transitions properly
        """
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists and get current status + items
                cursor.execute("""
                    SELECT o.status, oi.sku, oi.quantity
                    FROM orders o
                    LEFT JOIN order_items oi ON o.order_id = oi.order_id
                    WHERE o.order_id = %s
                """, (order_id,))

                rows = cursor.fetchall()
                if not rows:
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                current_status = rows[0][0]
                order_items = [{'sku': row[1], 'quantity': row[2]} for row in rows if row[1]]

                # Handle inventory based on status change
                inventory_action_result = None

                if status == 'confirmed' and current_status == 'pending':
                    # Confirm stock usage (reduce total stock, keep reservation)
                    logger.info(f"Confirming stock usage for order {order_id}")
                    success = self.inventory_service.confirm_stock_usage_for_order(order_items, order_id)
                    inventory_action_result = "stock_confirmed" if success else "stock_confirmation_failed"

                elif status == 'cancelled':
                    # Release reserved stock
                    logger.info(f"Releasing reserved stock for cancelled order {order_id}")
                    success = self.inventory_service.release_stock_for_order(order_items, order_id)
                    inventory_action_result = "stock_released" if success else "stock_release_failed"

                elif status == 'delivered' and current_status == 'confirmed':
                    # No inventory action needed - stock was already confirmed
                    inventory_action_result = "no_inventory_action_needed"

                # Update order status in database
                cursor.execute("""
                    UPDATE orders
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                # Update order items status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                """, (status, order_id))

                conn.commit()
                logger.info(f"Order {order_id} status updated to {status} with inventory action: {inventory_action_result}")

                return {
                    "success": True,
                    "message": f"Order status updated to {status}",
                    "order_id": order_id,
                    "previous_status": current_status,
                    "inventory_action": inventory_action_result
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update order status {order_id}: {e}")
            raise

    async def update_item_status(self, order_id: str, sku: str, status: str, db_conn=None) -> Dict:
        """Update status of a specific item within an order"""
        conn = db_conn or self.db_conn
        if not conn:
            raise ValueError("Database connection is required for update operation")

        try:
            with conn.cursor() as cursor:
                # Check if order exists
                cursor.execute("SELECT id FROM orders WHERE order_id = %s", (order_id,))
                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": "Order not found"
                    }

                # Check if item exists in the order
                cursor.execute("""
                    SELECT id FROM order_items
                    WHERE order_id = %s AND sku = %s
                """, (order_id, sku))

                if not cursor.fetchone():
                    return {
                        "success": False,
                        "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                    }

                # Update specific item status
                cursor.execute("""
                    UPDATE order_items
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s AND sku = %s
                """, (status, order_id, sku))

                conn.commit()
                logger.info(f"Item {sku} in order {order_id} status updated to {status}")

                return {
                    "success": True,
                    "message": f"Item '{sku}' status updated to '{status}'",
                    "order_id": order_id,
                    "sku": sku,
                    "status": status
                }

        except Exception as e:
            conn.rollback()
            logger.error(f"Failed to update item status {order_id}/{sku}: {e}")
            raise

    async def create_bulk_orders(self, orders, user_id: str, db_conn) -> list[dict[str, str]]:        
        now = datetime.utcnow()
        eta_default = now + timedelta(hours=24)
        
        if not orders:
            return []

        try:
            with db_conn.cursor() as cursor:
                order_data_list = []
                item_data_list = []
                
                for order_idx, order in enumerate(orders):
                    if hasattr(order, 'model_dump'):
                        order_data = order.model_dump()
                    else:
                        order_data = order
                    
                    random_prefix = generate_random_prefix()
                    
                    order_data_list.append({
                        'order_idx': order_idx,
                        'random_prefix': random_prefix,
                        'customer_id': order_data['customer_id'],
                        'customer_name': order_data['customer_name'],
                        'facility_id': order_data['facility_id'],
                        'facility_name': order_data['facility_name'],
                        'status': order_data['status'],
                        'total_amount': order_data['total_amount'],
                        'eta': eta_default
                    })
                    
                    if order_data.get('items'):
                        for item in order_data['items']:
                            item_data_list.append({
                                'order_idx': order_idx,
                                'sku': item['sku'],
                                'quantity': item['quantity'],
                                'unit_price': item['unit_price'],
                                'sale_price': item['sale_price'],
                                'status': order_data['status']
                            })

                order_values_clause = []
                order_params = {}
                
                for i, order_data in enumerate(order_data_list):
                    order_values_clause.append(
                        f"(%(random_prefix_{i})s, %(customer_id_{i})s, %(customer_name_{i})s, "
                        f"%(facility_id_{i})s, %(facility_name_{i})s, %(status_{i})s, %(total_amount_{i})s, %(eta_{i})s)"
                    )
                    order_params.update({
                        f'random_prefix_{i}': order_data['random_prefix'],
                        f'customer_id_{i}': order_data['customer_id'],
                        f'customer_name_{i}': order_data['customer_name'],
                        f'facility_id_{i}': order_data['facility_id'],
                        f'facility_name_{i}': order_data['facility_name'],
                        f'status_{i}': order_data['status'],
                        f'total_amount_{i}': order_data['total_amount'],
                        f'eta_{i}': order_data['eta']
                    })

                item_values_clause = []
                item_params = {}
                
                if item_data_list:
                    for i, item_data in enumerate(item_data_list):
                        item_values_clause.append(
                            f"(%(item_order_idx_{i})s, %(sku_{i})s, %(quantity_{i})s, "
                            f"%(unit_price_{i})s, %(sale_price_{i})s, %(item_status_{i})s)"
                        )
                        item_params.update({
                            f'item_order_idx_{i}': item_data['order_idx'],
                            f'sku_{i}': item_data['sku'],
                            f'quantity_{i}': item_data['quantity'],
                            f'unit_price_{i}': item_data['unit_price'],
                            f'sale_price_{i}': item_data['sale_price'],
                            f'item_status_{i}': item_data['status']
                        })

                if item_data_list:
                    # Create a temporary table to map order indices to order IDs
                    query = f"""
                    WITH order_mapping AS (
                        SELECT 
                            ROW_NUMBER() OVER () - 1 as order_idx,
                            random_prefix,
                            customer_id,
                            customer_name,
                            facility_id,
                            facility_name,
                            status,
                            total_amount,
                            eta
                        FROM (
                            VALUES {', '.join(order_values_clause)}
                        ) AS v(random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta)
                    ),
                    inserted_orders AS (
                        INSERT INTO orders (
                            random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta
                        )
                        SELECT random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta
                        FROM order_mapping
                        ORDER BY order_idx
                        RETURNING order_id, random_prefix
                    ),
                    order_id_mapping AS (
                        SELECT 
                            io.order_id,
                            io.random_prefix,
                            ROW_NUMBER() OVER () - 1 as order_idx
                        FROM inserted_orders io
                    ),
                    inserted_items AS (
                        INSERT INTO order_items (order_id, sku, quantity, unit_price, sale_price, status)
                        SELECT 
                            oim.order_id,
                            item.sku,
                            item.quantity,
                            item.unit_price,
                            item.sale_price,
                            item.status
                        FROM order_id_mapping oim
                        JOIN (
                            VALUES {', '.join(item_values_clause)}
                        ) AS item(order_idx, sku, quantity, unit_price, sale_price, status)
                        ON oim.order_idx = item.order_idx
                        RETURNING id, order_id, sku, quantity, unit_price, sale_price
                    )
                    SELECT 
                        oim.order_id,
                        oim.random_prefix,
                        oim.order_idx
                    FROM order_id_mapping oim
                    ORDER BY oim.order_idx;
                    """
                else:
                    query = f"""
                    INSERT INTO orders (
                        random_prefix, customer_id, customer_name, facility_id, facility_name, status, total_amount, eta
                    )
                    VALUES {', '.join(order_values_clause)}
                    RETURNING order_id, random_prefix;
                    """

                all_params = {**order_params, **item_params}
                
                cursor.execute(query, all_params)
                rows = cursor.fetchall()

                result = []
                for row in rows:
                    result.append({
                        "order_id": row[0],
                        "eta": eta_default.isoformat()
                    })

                db_conn.commit()
                logger.info(f"Successfully created {len(orders)} orders in bulk using single query")

        except Exception as e:
            db_conn.rollback()
            logger.error(f"Failed to create bulk orders: {e}")
            raise

        return result