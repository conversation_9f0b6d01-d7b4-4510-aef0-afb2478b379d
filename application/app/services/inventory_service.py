from typing import Dict, List, Optional, Tuple
import logging
from app.redis_client import inventory_manager
from app.models.inventory import (
    StockItem, StockResponse, StockOperationResponse, 
    BulkStockOperationResponse, InventoryStatus
)

logger = logging.getLogger(__name__)

class InventoryService:
    """
    Service for managing inventory operations with Redis
    
    Why needed:
    - Centralizes all inventory-related business logic
    - Provides high-level interface for stock operations
    - Handles complex operations like bulk updates and reservations
    - Ensures data consistency and proper error handling
    """
    
    def __init__(self):
        """
        Initialize inventory service with Redis manager
        
        Why needed:
        - Uses global inventory manager for Redis operations
        - Ensures consistent Redis connection handling
        - Provides clean separation of concerns
        """
        self.inventory_manager = inventory_manager
    
    def set_stock(self, sku: str, quantity: int) -> StockOperationResponse:
        """
        Set stock quantity for a specific SKU
        
        Args:
            sku: Product SKU
            quantity: Stock quantity to set
            
        Returns:
            StockOperationResponse: Operation result
            
        Why needed:
        - Provides business logic layer over Redis operations
        - Validates input and handles errors gracefully
        - Returns standardized response format
        - Used by inventory management APIs
        """
        try:
            if quantity < 0:
                return StockOperationResponse(
                    success=False,
                    message="Stock quantity cannot be negative",
                    sku=sku,
                    quantity=quantity
                )
            
            success = self.inventory_manager.set_stock(sku, quantity)
            
            if success:
                logger.info(f"Stock set successfully for {sku}: {quantity}")
                return StockOperationResponse(
                    success=True,
                    message=f"Stock set successfully for {sku}",
                    sku=sku,
                    quantity=quantity
                )
            else:
                return StockOperationResponse(
                    success=False,
                    message=f"Failed to set stock for {sku}",
                    sku=sku,
                    quantity=quantity
                )
                
        except Exception as e:
            logger.error(f"Error setting stock for {sku}: {e}")
            return StockOperationResponse(
                success=False,
                message=f"Internal error setting stock for {sku}",
                sku=sku,
                quantity=quantity
            )
    
    def get_stock_info(self, sku: str) -> StockResponse:
        """
        Get comprehensive stock information for a SKU
        
        Args:
            sku: Product SKU
            
        Returns:
            StockResponse: Complete stock information
            
        Why needed:
        - Provides detailed stock information including reservations
        - Used by inventory queries and order validation
        - Calculates available stock automatically
        - Standardized response format for consistency
        """
        try:
            total_stock = self.inventory_manager.get_stock(sku)
            available_stock = self.inventory_manager.get_available_stock(sku)
            reserved_stock = total_stock - available_stock
            
            return StockResponse(
                sku=sku,
                total_stock=total_stock,
                reserved_stock=reserved_stock,
                available_stock=available_stock
            )
            
        except Exception as e:
            logger.error(f"Error getting stock info for {sku}: {e}")
            return StockResponse(
                sku=sku,
                total_stock=0,
                reserved_stock=0,
                available_stock=0
            )
    
    def check_stock_availability(self, sku: str, required_quantity: int) -> Tuple[bool, str]:
        """
        Check if sufficient stock is available for a specific quantity
        
        Args:
            sku: Product SKU
            required_quantity: Required quantity to check
            
        Returns:
            Tuple[bool, str]: (availability_status, message)
            
        Why needed:
        - Critical for order validation before creation
        - Prevents overselling and stock issues
        - Provides clear feedback on availability status
        - Used in order creation flow
        """
        try:
            if required_quantity <= 0:
                return False, "Required quantity must be greater than 0"
            
            available_stock = self.inventory_manager.get_available_stock(sku)
            
            if available_stock >= required_quantity:
                return True, f"Stock available: {available_stock} units"
            else:
                return False, f"Insufficient stock: required {required_quantity}, available {available_stock}"
                
        except Exception as e:
            logger.error(f"Error checking stock availability for {sku}: {e}")
            return False, f"Error checking stock availability for {sku}"
    
    def bulk_check_availability(self, items: List[Dict[str, any]]) -> Tuple[bool, List[Dict[str, any]]]:
        """
        Check stock availability for multiple items
        
        Args:
            items: List of items with sku and quantity
            
        Returns:
            Tuple[bool, List[Dict]]: (all_available, detailed_results)
            
        Why needed:
        - Validates entire order before processing
        - Provides detailed feedback for each item
        - Prevents partial order creation
        - Efficient batch processing
        """
        try:
            results = []
            all_available = True
            
            for item in items:
                sku = item.get('sku')
                quantity = item.get('quantity', 0)
                
                available, message = self.check_stock_availability(sku, quantity)
                
                results.append({
                    'sku': sku,
                    'required_quantity': quantity,
                    'available': available,
                    'message': message
                })
                
                if not available:
                    all_available = False
            
            return all_available, results
            
        except Exception as e:
            logger.error(f"Error in bulk stock availability check: {e}")
            return False, [{'error': 'Internal error during stock check'}]
    
    def reserve_stock_for_order(self, items: List[Dict[str, any]], order_id: str = None) -> Tuple[bool, List[Dict[str, any]]]:
        """
        Reserve stock for multiple items (atomic operation)
        
        Args:
            items: List of items with sku and quantity
            order_id: Optional order ID for tracking
            
        Returns:
            Tuple[bool, List[Dict]]: (success, detailed_results)
            
        Why needed:
        - Atomic reservation prevents race conditions
        - Ensures stock is held during order processing
        - Provides rollback capability on failure
        - Critical for preventing overselling
        """
        try:
            # First, check availability for all items
            all_available, availability_results = self.bulk_check_availability(items)
            
            if not all_available:
                return False, availability_results
            
            # Attempt to reserve all items
            reservation_results = []
            reserved_items = []  # Track successful reservations for rollback
            
            for item in items:
                sku = item.get('sku')
                quantity = item.get('quantity', 0)
                
                success = self.inventory_manager.reserve_stock(sku, quantity)
                
                if success:
                    reserved_items.append({'sku': sku, 'quantity': quantity})
                    reservation_results.append({
                        'sku': sku,
                        'quantity': quantity,
                        'reserved': True,
                        'message': f'Reserved {quantity} units of {sku}'
                    })
                else:
                    # Rollback previous reservations
                    for reserved_item in reserved_items:
                        self.inventory_manager.release_stock(
                            reserved_item['sku'], 
                            reserved_item['quantity']
                        )
                    
                    reservation_results.append({
                        'sku': sku,
                        'quantity': quantity,
                        'reserved': False,
                        'message': f'Failed to reserve {quantity} units of {sku}'
                    })
                    
                    logger.error(f"Stock reservation failed for order {order_id}, rolled back previous reservations")
                    return False, reservation_results
            
            logger.info(f"Successfully reserved stock for order {order_id}")
            return True, reservation_results
            
        except Exception as e:
            logger.error(f"Error reserving stock for order {order_id}: {e}")
            return False, [{'error': 'Internal error during stock reservation'}]
    
    def confirm_stock_usage_for_order(self, items: List[Dict[str, any]], order_id: str = None) -> bool:
        """
        Confirm stock usage for order (reduces total and reserved stock)
        
        Args:
            items: List of items with sku and quantity
            order_id: Optional order ID for tracking
            
        Returns:
            bool: Success status
            
        Why needed:
        - Finalizes stock usage when order is confirmed
        - Reduces both total stock and reserved stock
        - Completes the stock reservation lifecycle
        - Maintains accurate inventory levels
        """
        try:
            for item in items:
                sku = item.get('sku')
                quantity = item.get('quantity', 0)
                
                success = self.inventory_manager.confirm_stock_usage(sku, quantity)
                if not success:
                    logger.error(f"Failed to confirm stock usage for {sku} in order {order_id}")
                    return False
            
            logger.info(f"Confirmed stock usage for order {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error confirming stock usage for order {order_id}: {e}")
            return False
    
    def release_stock_for_order(self, items: List[Dict[str, any]], order_id: str = None) -> bool:
        """
        Release reserved stock for order (e.g., when order is cancelled)
        
        Args:
            items: List of items with sku and quantity
            order_id: Optional order ID for tracking
            
        Returns:
            bool: Success status
            
        Why needed:
        - Releases stock when orders are cancelled or fail
        - Prevents stock from being permanently locked
        - Maintains accurate available stock levels
        - Part of order cancellation process
        """
        try:
            for item in items:
                sku = item.get('sku')
                quantity = item.get('quantity', 0)
                
                success = self.inventory_manager.release_stock(sku, quantity)
                if not success:
                    logger.error(f"Failed to release stock for {sku} in order {order_id}")
                    return False
            
            logger.info(f"Released stock for order {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error releasing stock for order {order_id}: {e}")
            return False
    
    def bulk_set_stock(self, items: List[StockItem]) -> BulkStockOperationResponse:
        """
        Set stock for multiple SKUs in bulk
        
        Args:
            items: List of StockItem objects
            
        Returns:
            BulkStockOperationResponse: Bulk operation results
            
        Why needed:
        - Efficient bulk inventory updates
        - Used for initial inventory setup
        - Provides detailed results for each item
        - Handles partial success scenarios
        """
        try:
            results = []
            successful_count = 0
            failed_count = 0
            
            for item in items:
                result = self.set_stock(item.sku, item.quantity)
                results.append({
                    'sku': item.sku,
                    'quantity': item.quantity,
                    'success': result.success,
                    'message': result.message
                })
                
                if result.success:
                    successful_count += 1
                else:
                    failed_count += 1
            
            overall_success = failed_count == 0
            
            return BulkStockOperationResponse(
                success=overall_success,
                message=f"Bulk stock update completed: {successful_count} successful, {failed_count} failed",
                results=results,
                successful_count=successful_count,
                failed_count=failed_count
            )
            
        except Exception as e:
            logger.error(f"Error in bulk stock update: {e}")
            return BulkStockOperationResponse(
                success=False,
                message="Internal error during bulk stock update",
                results=[],
                successful_count=0,
                failed_count=len(items)
            )
