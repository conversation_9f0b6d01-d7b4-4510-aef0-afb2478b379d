from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from datetime import datetime

class OrderItemCreate(BaseModel):
    sku: str = Field(..., min_length=1, max_length=100)
    quantity: int = Field(..., gt=0)
    unit_price: float = Field(..., gt=0)
    sale_price: float = Field(..., gt=0)

class OrderCreate(BaseModel):
    customer_id: str = Field(..., min_length=1, max_length=50)
    customer_name: str = Field(..., min_length=1, max_length=100)
    facility_id: str = Field(..., min_length=1, max_length=50)
    facility_name: str = Field(..., min_length=1, max_length=100)
    status: str = Field("pending", pattern="^(pending|confirmed|delivered|cancelled)$")
    total_amount: float = Field(..., gt=0)
    items: List[OrderItemCreate]

class OrderStatusUpdate(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50)
    status: str = Field(..., pattern="^(pending|confirmed|delivered|cancelled)$")

class OrderItemStatusUpdate(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50)
    sku: str = Field(..., min_length=1, max_length=100)
    status: str = Field(..., pattern="^(pending|confirmed|delivered|cancelled|refunded)$")

class OrderResponse(BaseModel):
    success: bool
    message: str
    order_id: str
    eta: Optional[datetime] = None

class BulkOrderCreate(BaseModel):
    orders: List[OrderCreate]

class BulkOrderResponse(BaseModel):
    success: bool
    message: str
    results: List[Dict[str, str]]