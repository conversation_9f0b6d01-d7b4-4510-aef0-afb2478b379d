from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any

class StockItem(BaseModel):
    """
    Model for individual stock item
    
    Why needed:
    - Validates stock data structure
    - Ensures SKU and quantity are properly formatted
    - Used for setting initial stock levels
    """
    sku: str = Field(..., min_length=1, max_length=100, description="Product SKU")
    quantity: int = Field(..., ge=0, description="Stock quantity (must be >= 0)")

class StockUpdate(BaseModel):
    """
    Model for updating stock levels
    
    Why needed:
    - Validates stock update requests
    - Supports both individual and bulk stock updates
    - Ensures data integrity for inventory operations
    """
    sku: str = Field(..., min_length=1, max_length=100, description="Product SKU")
    quantity: int = Field(..., ge=0, description="New stock quantity")

class BulkStockUpdate(BaseModel):
    """
    Model for bulk stock updates
    
    Why needed:
    - Allows updating multiple SKUs in single request
    - Improves efficiency for inventory management
    - Validates entire batch before processing
    """
    items: List[StockItem] = Field(..., min_items=1, description="List of stock items to update")

class StockCheck(BaseModel):
    """
    Model for stock availability check
    
    Why needed:
    - Validates stock check requests
    - Used before order creation to verify availability
    - Supports checking multiple SKUs at once
    """
    sku: str = Field(..., min_length=1, max_length=100, description="Product SKU")
    required_quantity: int = Field(..., gt=0, description="Required quantity to check")

class BulkStockCheck(BaseModel):
    """
    Model for checking multiple SKUs at once
    
    Why needed:
    - Validates bulk stock check requests
    - Used for order validation with multiple items
    - Efficient batch processing
    """
    items: List[StockCheck] = Field(..., min_items=1, description="List of items to check")

class StockResponse(BaseModel):
    """
    Model for stock query responses
    
    Why needed:
    - Standardizes stock information response format
    - Provides both total and available stock information
    - Clear distinction between reserved and available stock
    """
    sku: str = Field(..., description="Product SKU")
    total_stock: int = Field(..., description="Total stock quantity")
    reserved_stock: int = Field(..., description="Reserved stock quantity")
    available_stock: int = Field(..., description="Available stock quantity")

class StockOperationResponse(BaseModel):
    """
    Model for stock operation responses
    
    Why needed:
    - Standardizes response format for stock operations
    - Provides success/failure status with descriptive messages
    - Includes relevant data for debugging and logging
    """
    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Operation result message")
    sku: Optional[str] = Field(None, description="SKU involved in operation")
    quantity: Optional[int] = Field(None, description="Quantity involved in operation")

class BulkStockOperationResponse(BaseModel):
    """
    Model for bulk stock operation responses
    
    Why needed:
    - Handles responses for bulk operations
    - Provides detailed results for each item
    - Allows partial success scenarios
    """
    success: bool = Field(..., description="Overall operation success status")
    message: str = Field(..., description="Overall operation message")
    results: List[Dict[str, Any]] = Field(..., description="Individual operation results")
    successful_count: int = Field(..., description="Number of successful operations")
    failed_count: int = Field(..., description="Number of failed operations")

class StockReservation(BaseModel):
    """
    Model for stock reservation operations
    
    Why needed:
    - Validates stock reservation requests
    - Used during order creation process
    - Ensures proper data structure for reservations
    """
    sku: str = Field(..., min_length=1, max_length=100, description="Product SKU")
    quantity: int = Field(..., gt=0, description="Quantity to reserve")
    order_id: Optional[str] = Field(None, description="Associated order ID")

class BulkStockReservation(BaseModel):
    """
    Model for bulk stock reservation
    
    Why needed:
    - Handles reservation of multiple SKUs for single order
    - Atomic reservation for entire order
    - Prevents partial reservations that could cause issues
    """
    items: List[StockReservation] = Field(..., min_items=1, description="Items to reserve")
    order_id: Optional[str] = Field(None, description="Associated order ID")

class InventoryStatus(BaseModel):
    """
    Model for overall inventory status
    
    Why needed:
    - Provides comprehensive inventory overview
    - Used for inventory management dashboards
    - Helps monitor system health
    """
    total_skus: int = Field(..., description="Total number of SKUs in inventory")
    total_stock: int = Field(..., description="Total stock across all SKUs")
    total_reserved: int = Field(..., description="Total reserved stock")
    total_available: int = Field(..., description="Total available stock")
    low_stock_skus: List[str] = Field(..., description="SKUs with low stock levels")
