from psycopg_pool import ConnectionPool
from contextlib import asynccontextmanager
import os
import logging
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

WRITE_DB_URL = os.getenv("WRITE_DB_URL")
READ_DB_URL = os.getenv("READ_DB_URL")

# Database connection pools
db_pool = None
read_db_pool = None


def get_db_pool():
    """Get write database connection pool (DB1)"""
    global db_pool
    if db_pool is None:
        try:
            if not WRITE_DB_URL:
                raise ValueError("WRITE_DB_URL environment variable is required")
            
            db_pool = ConnectionPool(
                conninfo=WRITE_DB_URL,
                min_size=5,
                max_size=50,
                kwargs={
                    "keepalives_idle": 600,
                    "keepalives_interval": 30,
                    "keepalives_count": 3
                }
            )
            logger.info("Write database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise
    return db_pool


def get_read_db_pool():
    """Get read database connection pool (READ_DB)"""
    global read_db_pool
    if read_db_pool is None:
        try:
            if not READ_DB_URL:
                raise ValueError("READ_DB_URL environment variable is required")
        
            read_db_pool = ConnectionPool(
                conninfo=READ_DB_URL,
                min_size=5,
                max_size=30,
                kwargs={
                    "keepalives_idle": 600,
                    "keepalives_interval": 30,
                    "keepalives_count": 3
                }
            )
            logger.info("Read database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize read database pool: {e}")
            raise
    return read_db_pool

@asynccontextmanager
async def get_write_db():
    """Get write database connection (WRITE_DB) for INSERT, UPDATE, DELETE operations"""
    pool = get_db_pool()
    try:
        with pool.connection() as conn:
            conn.autocommit = False
            logger.debug("Write database connection acquired")
            yield conn
    except Exception as e:
        logger.error(f"Error with database connection: {e}")
        raise

# Backward compatibility alias
@asynccontextmanager  
async def get_db():
    """Get database connection (defaults to write database for backward compatibility)"""
    async with get_write_db() as conn:
        yield conn

@asynccontextmanager
async def get_read_db():
    """Get read database connection (READ_DB) for SELECT operations"""
    pool = get_read_db_pool()
    try:
        with pool.connection() as conn:
            conn.autocommit = True
            with conn.cursor() as cursor:
                cursor.execute("SET TRANSACTION READ ONLY")
            logger.debug("Read-only database connection acquired")
            yield conn
    except Exception as e:
        logger.error(f"Error with read-only database connection: {e}")
        raise

@asynccontextmanager
async def get_db_readonly():
    """Get database connection optimized for read-only operations (backward compatibility)"""
    async with get_read_db() as conn:
        yield conn

def close_db_pool():
    """Close all database connection pools"""
    global db_pool, read_db_pool
    if db_pool:
        try:
            db_pool.close()
            db_pool = None
            logger.info("Write database pool closed")
        except Exception as e:
            logger.error(f"Error closing database pool: {e}")
    if read_db_pool:
        try:
            read_db_pool.close()
            read_db_pool = None
            logger.info("Read database pool closed")
        except Exception as e:
            logger.error(f"Error closing read database pool: {e}")
        finally:
            read_db_pool = None

# Database lifecycle functions
async def startup_database():
    """Initialize database connections for production"""
    # Initialize connection pools
    get_db_pool()
    get_read_db_pool()

    # Initialize Redis connections
    from app.redis_client import startup_redis
    await startup_redis()

    logger.info("Database and Redis systems initialized successfully")


async def shutdown_database():
    """Shutdown database connections"""
    # Close connection pools
    close_db_pool()

    # Shutdown Redis connections
    from app.redis_client import shutdown_redis
    await shutdown_redis()

    logger.info("Database and Redis systems shutdown complete")

