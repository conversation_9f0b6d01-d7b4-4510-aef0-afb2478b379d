import redis
import os
import logging
from contextlib import asynccontextmanager
from typing import Optional
import json
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis configuration from environment variables
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None)

# Global Redis connection pool
redis_pool = None


def get_redis_pool():
    """
    Get Redis connection pool (singleton pattern)
    
    Returns:
        redis.ConnectionPool: Redis connection pool instance
        
    Why needed:
    - Connection pooling prevents creating new connections for each request
    - Singleton pattern ensures only one pool is created
    - Improves performance and reduces connection overhead
    """
    global redis_pool
    if redis_pool is None:
        try:
            redis_pool = redis.ConnectionPool(
                host=REDIS_HOST,
                port=REDIS_PORT,
                db=REDIS_DB,
                password=REDIS_PASSWORD,
                decode_responses=True,  # Automatically decode bytes to strings
                max_connections=20,     # Maximum connections in pool
                retry_on_timeout=True,  # Retry on timeout
                socket_keepalive=True,  # Keep connections alive
                socket_keepalive_options={}
            )
            logger.info(f"Redis connection pool initialized: {REDIS_HOST}:{REDIS_PORT}")
        except Exception as e:
            logger.error(f"Failed to initialize Redis pool: {e}")
            raise
    return redis_pool


def get_redis_client():
    """
    Get Redis client from connection pool
    
    Returns:
        redis.Redis: Redis client instance
        
    Why needed:
    - Provides Redis client with connection pooling
    - Handles connection management automatically
    - Thread-safe and efficient for concurrent requests
    """
    pool = get_redis_pool()
    return redis.Redis(connection_pool=pool)


@asynccontextmanager
async def get_redis():
    """
    Async context manager for Redis operations
    
    Yields:
        redis.Redis: Redis client instance
        
    Why needed:
    - Provides async-compatible Redis access
    - Ensures proper connection cleanup
    - Compatible with FastAPI's async/await pattern
    """
    client = get_redis_client()
    try:
        yield client
    except Exception as e:
        logger.error(f"Redis operation error: {e}")
        raise
    finally:
        # Connection is automatically returned to pool
        pass


class RedisInventoryManager:
    """
    Redis-based inventory management system
    
    Why needed:
    - Fast in-memory stock checking and updates
    - Atomic operations for stock reservation
    - High performance for concurrent order processing
    """
    
    def __init__(self):
        self.client = get_redis_client()
        self.stock_prefix = "stock:"  # Redis key prefix for stock data
        self.reserved_prefix = "reserved:"  # Redis key prefix for reserved stock
    
    def _get_stock_key(self, sku: str) -> str:
        """Generate Redis key for stock data"""
        return f"{self.stock_prefix}{sku}"
    
    def _get_reserved_key(self, sku: str) -> str:
        """Generate Redis key for reserved stock data"""
        return f"{self.reserved_prefix}{sku}"
    
    def set_stock(self, sku: str, quantity: int) -> bool:
        """
        Set stock quantity for a SKU
        
        Args:
            sku: Product SKU
            quantity: Stock quantity to set
            
        Returns:
            bool: True if successful
            
        Why needed:
        - Initialize or update stock levels
        - Used by inventory management APIs
        - Atomic operation ensures data consistency
        """
        try:
            stock_key = self._get_stock_key(sku)
            self.client.set(stock_key, quantity)
            logger.info(f"Stock set for {sku}: {quantity}")
            return True
        except Exception as e:
            logger.error(f"Failed to set stock for {sku}: {e}")
            return False
    
    def get_stock(self, sku: str) -> int:
        """
        Get current stock quantity for a SKU
        
        Args:
            sku: Product SKU
            
        Returns:
            int: Current stock quantity (0 if not found)
            
        Why needed:
        - Check available stock before order creation
        - Display current inventory levels
        - Fast Redis lookup for real-time data
        """
        try:
            stock_key = self._get_stock_key(sku)
            stock = self.client.get(stock_key)
            return int(stock) if stock else 0
        except Exception as e:
            logger.error(f"Failed to get stock for {sku}: {e}")
            return 0
    
    def get_available_stock(self, sku: str) -> int:
        """
        Get available stock (total stock - reserved stock)
        
        Args:
            sku: Product SKU
            
        Returns:
            int: Available stock quantity
            
        Why needed:
        - Calculate actual available stock for orders
        - Considers both total stock and reserved quantities
        - Prevents overselling during concurrent orders
        """
        try:
            total_stock = self.get_stock(sku)
            reserved_key = self._get_reserved_key(sku)
            reserved = self.client.get(reserved_key)
            reserved_stock = int(reserved) if reserved else 0
            available = max(0, total_stock - reserved_stock)
            logger.debug(f"Available stock for {sku}: {available} (total: {total_stock}, reserved: {reserved_stock})")
            return available
        except Exception as e:
            logger.error(f"Failed to get available stock for {sku}: {e}")
            return 0
    
    def reserve_stock(self, sku: str, quantity: int) -> bool:
        """
        Reserve stock for order processing (atomic operation)
        
        Args:
            sku: Product SKU
            quantity: Quantity to reserve
            
        Returns:
            bool: True if reservation successful, False if insufficient stock
            
        Why needed:
        - Atomic stock reservation prevents race conditions
        - Ensures stock is held during order processing
        - Critical for preventing overselling in concurrent scenarios
        """
        try:
            # Use Redis pipeline for atomic operations
            pipe = self.client.pipeline()
            stock_key = self._get_stock_key(sku)
            reserved_key = self._get_reserved_key(sku)
            
            # Watch keys for changes (optimistic locking)
            pipe.watch(stock_key, reserved_key)
            
            # Get current values
            current_stock = self.get_stock(sku)
            current_reserved = int(self.client.get(reserved_key) or 0)
            available = current_stock - current_reserved
            
            if available < quantity:
                pipe.unwatch()
                logger.warning(f"Insufficient stock for {sku}: requested {quantity}, available {available}")
                return False
            
            # Execute atomic reservation
            pipe.multi()
            pipe.incrby(reserved_key, quantity)
            pipe.execute()
            
            logger.info(f"Reserved {quantity} units of {sku}")
            return True
            
        except redis.WatchError:
            logger.warning(f"Stock reservation failed due to concurrent modification for {sku}")
            return False
        except Exception as e:
            logger.error(f"Failed to reserve stock for {sku}: {e}")
            return False
    
    def release_stock(self, sku: str, quantity: int) -> bool:
        """
        Release reserved stock (e.g., when order is cancelled)
        
        Args:
            sku: Product SKU
            quantity: Quantity to release
            
        Returns:
            bool: True if successful
            
        Why needed:
        - Release stock when orders are cancelled or fail
        - Maintains accurate available stock levels
        - Prevents stock from being permanently locked
        """
        try:
            reserved_key = self._get_reserved_key(sku)
            current_reserved = int(self.client.get(reserved_key) or 0)
            new_reserved = max(0, current_reserved - quantity)
            self.client.set(reserved_key, new_reserved)
            logger.info(f"Released {quantity} units of {sku}")
            return True
        except Exception as e:
            logger.error(f"Failed to release stock for {sku}: {e}")
            return False
    
    def confirm_stock_usage(self, sku: str, quantity: int) -> bool:
        """
        Confirm stock usage (reduce both total and reserved stock)
        
        Args:
            sku: Product SKU
            quantity: Quantity to confirm as used
            
        Returns:
            bool: True if successful
            
        Why needed:
        - Finalizes stock usage when order is confirmed
        - Reduces both total stock and reserved stock
        - Completes the stock reservation cycle
        """
        try:
            pipe = self.client.pipeline()
            stock_key = self._get_stock_key(sku)
            reserved_key = self._get_reserved_key(sku)
            
            # Atomic operation to reduce both stock and reserved
            pipe.decrby(stock_key, quantity)
            pipe.decrby(reserved_key, quantity)
            pipe.execute()
            
            logger.info(f"Confirmed usage of {quantity} units of {sku}")
            return True
        except Exception as e:
            logger.error(f"Failed to confirm stock usage for {sku}: {e}")
            return False


# Global inventory manager instance
inventory_manager = RedisInventoryManager()


def close_redis_pool():
    """
    Close Redis connection pool
    
    Why needed:
    - Clean shutdown of Redis connections
    - Called during application shutdown
    - Prevents connection leaks
    """
    global redis_pool
    if redis_pool:
        try:
            redis_pool.disconnect()
            redis_pool = None
            logger.info("Redis connection pool closed")
        except Exception as e:
            logger.error(f"Error closing Redis pool: {e}")


# Startup and shutdown functions
async def startup_redis():
    """
    Initialize Redis connections for production
    
    Why needed:
    - Ensures Redis is available at startup
    - Tests connection before accepting requests
    - Part of application lifecycle management
    """
    try:
        client = get_redis_client()
        client.ping()  # Test connection
        logger.info("Redis connection initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Redis: {e}")
        raise


async def shutdown_redis():
    """
    Shutdown Redis connections
    
    Why needed:
    - Clean shutdown of Redis resources
    - Called during application shutdown
    - Ensures proper cleanup
    """
    close_redis_pool()
    logger.info("Redis system shutdown complete")
